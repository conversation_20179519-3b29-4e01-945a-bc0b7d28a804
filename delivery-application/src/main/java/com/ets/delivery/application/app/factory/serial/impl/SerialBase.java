package com.ets.delivery.application.app.factory.serial.impl;

import com.ets.delivery.application.app.factory.serial.ISerial;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.infra.entity.Serial;
import com.ets.delivery.application.infra.entity.SerialLog;
import com.ets.delivery.application.infra.service.SerialLogService;
import com.ets.delivery.application.infra.service.SerialService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 序列号处理器基类
 * 提供通用的序列号处理逻辑
 */
@Slf4j
public abstract class SerialBase implements ISerial {
    
    @Autowired
    protected SerialService serialService;
    
    @Autowired
    protected SerialLogService serialLogService;

    @Autowired
    protected WorkWeChatBusiness workWeChatBusiness;

    @Autowired
    protected WeChatRobotConfig weChatRobotConfig;
    
    private static final Pattern SERIAL_PATTERN = Pattern.compile("^\\d{16}$");
    
    @Override
    public void process(SerialProcessBO processBO) {
        if (ObjectUtils.isEmpty(processBO.getSerialList())) {
            log.warn("序列号列表为空，跳过处理");
            return;
        }
        
        // 验证序列号格式
        List<String> validSerials = validateSerialNumbers(processBO.getSerialList(), 
                processBO.getBusinessSn(), processBO.getStorageSku());
        processBO.setSerialList(validSerials);
        
        if (validSerials.isEmpty()) {
            log.warn("所有序列号都不符合格式要求，跳过处理");
            return;
        }
        
        // 设置目标状态
        setTargetStatus(processBO);
        
        // 处理序列号
        SerialProcessResult result = new SerialProcessResult();
        
        // 批量查询现有序列号
        List<Serial> existingSerials = serialService.getBySerialNos(processBO.getSerialList());
        Map<String, Serial> existingSerialMap = existingSerials.stream()
                .collect(Collectors.toMap(Serial::getSerialNo, Function.identity()));
        
        // 处理每个序列号
        for (String serialNo : processBO.getSerialList()) {
            Serial existSerial = existingSerialMap.get(serialNo);
            Serial processedSerial = processSerial(serialNo, existSerial, processBO);
            
            if (existSerial != null) {
                result.addUpdate(processedSerial);
            } else {
                result.addNew(processedSerial);
            }
        }
        
        // 批量保存和更新
        batchSaveAndUpdate(result);

        // 批量创建日志记录
        batchCreateLogs(processBO);
        
        // 执行特定业务逻辑
        executeSpecificLogic(result, processBO);
    }
    
    /**
     * 设置目标状态（子类实现）
     */
    protected abstract void setTargetStatus(SerialProcessBO processBO);
    
    /**
     * 处理单个序列号（子类可重写）
     */
    protected Serial processSerial(String serialNo, Serial existSerial, SerialProcessBO processBO) {
        if (existSerial != null) {
            // 更新现有序列号
            updateExistingSerial(existSerial, processBO);
            return existSerial;
        } else {
            // 创建新序列号
            return createNewSerial(serialNo, processBO);
        }
    }
    
    /**
     * 更新现有序列号（子类可重写）
     */
    protected void updateExistingSerial(Serial existSerial, SerialProcessBO processBO) {
        existSerial.setStatus(processBO.getTargetStatus().getValue());
        if (StringUtils.isNotEmpty(processBO.getInventoryType())) {
            existSerial.setInventoryType(processBO.getInventoryType());
        }
        if (StringUtils.isNotEmpty(processBO.getStorageCode())) {
            existSerial.setStorageCode(processBO.getStorageCode());
        }
        existSerial.setLatestBusinessSource(processBO.getBusinessSource());
        existSerial.setLatestBusinessSn(processBO.getBusinessSn());
    }
    
    /**
     * 创建新序列号（子类可重写）
     */
    protected Serial createNewSerial(String serialNo, SerialProcessBO processBO) {
        Serial serial = new Serial();
        serial.setSerialNo(serialNo);
        serial.setStatus(processBO.getTargetStatus().getValue());
        serial.setInventoryType(processBO.getInventoryType());
        serial.setStorageCode(processBO.getStorageCode());
        serial.setStorageSku(processBO.getStorageSku());
        serial.setLatestBusinessSource(processBO.getBusinessSource());
        serial.setLatestBusinessSn(processBO.getBusinessSn());
        return serial;
    }
    
    /**
     * 执行特定业务逻辑（子类实现）
     */
    protected abstract void executeSpecificLogic(SerialProcessResult result, SerialProcessBO processBO);
    
    /**
     * 序列号处理结果
     */
    @Data
    protected static class SerialProcessResult {
        private final List<Serial> newSerials = new ArrayList<>();
        private final List<Serial> updateSerials = new ArrayList<>();
        
        public void addNew(Serial serial) {
            LocalDateTime now = LocalDateTime.now();
            serial.setCreatedAt(now);
            serial.setUpdatedAt(now);
            newSerials.add(serial);
        }
        
        public void addUpdate(Serial serial) {
            serial.setUpdatedAt(LocalDateTime.now());
            updateSerials.add(serial);
        }
    }
    
    /**
     * 批量保存和更新序列号
     */
    protected void batchSaveAndUpdate(SerialProcessResult result) {
        if (!result.getNewSerials().isEmpty()) {
            serialService.saveBatch(result.getNewSerials());
        }
        
        if (!result.getUpdateSerials().isEmpty()) {
            serialService.updateBatchById(result.getUpdateSerials());
        }
    }
    
    /**
     * 批量创建日志记录
     */
    protected void batchCreateLogs(SerialProcessBO processBO) {
        List<SerialLog> serialLogs = processBO.getSerialList().stream()
                .map(serialNo -> serialLogService.buildLog(serialNo, processBO.getTargetStatus().getValue(),
                        processBO.getBusinessSource(), processBO.getBusinessSn(), processBO.getOperator(), 
                        processBO.getRemark(), processBO.getInventoryType()))
                .collect(Collectors.toList());
        
        if (!serialLogs.isEmpty()) {
            serialLogService.saveBatch(serialLogs);
        }
    }
    
    /**
     * 处理不存在序列号的预警
     */
    protected void handleMissingSerialWarning(List<Serial> newSerials, SerialProcessBO processBO) {
        if (newSerials.isEmpty()) {
            return;
        }
        
        List<String> notFoundSerialNos = newSerials.stream()
                .map(Serial::getSerialNo)
                .collect(Collectors.toList());
        
        log.warn("序列号处理流程发现{}个不存在的序列号，已新增记录: 业务单号={}, SKU={}, 序列号列表={}",
                newSerials.size(), processBO.getBusinessSn(), 
                processBO.getStorageSku(), notFoundSerialNos);
        
        // 发送企微预警
        try {
            String markdown = String.format(
                "【序列号预警】<font color=\"warning\">发现不存在的序列号</font>\n" +
                "> 业务类型: <font color=\"info\">%s</font>\n" +
                "> 业务单号: <font color=\"info\">%s</font>\n" +
                "> SKU: <font color=\"info\">%s</font>\n" +
                "> 数量: <font color=\"info\">%d</font>个\n" +
                "> 序列号: <font color=\"info\">%s</font>",
                processBO.getBusinessSource(),
                processBO.getBusinessSn(),
                processBO.getStorageSku(),
                newSerials.size(),
                String.join(", ", notFoundSerialNos)
            );
            workWeChatBusiness.sendMarkdown(markdown, weChatRobotConfig.getSerialAlarmKey());
        } catch (Exception e) {
            log.error("发送序列号不存在预警失败: {}", e.getMessage());
        }
    }

    /**
     * 处理格式不符合序列号的预警
     */
    protected void handleInvalidFormatSerialWarning(List<String> invalidFormatSerials, String businessSn, String storageSku) {
        if (invalidFormatSerials.isEmpty()) {
            return;
        }
        
        log.warn("发现格式不符合的序列号（应为16位数字），但仍正常处理: 业务单号={}, SKU={}, 数量={}, 序列号列表={}",
                businessSn, storageSku, invalidFormatSerials.size(), invalidFormatSerials);
        
        // 发送企微预警
        try {
            String markdown = String.format(
                "【序列号预警】<font color=\"warning\">发现格式不符合的序列号</font>\n" +
                "> 业务单号: <font color=\"info\">%s</font>\n" +
                "> SKU: <font color=\"info\">%s</font>\n" +
                "> 数量: <font color=\"info\">%d</font>个\n" +
                "> 格式要求: 16位数字\n" +
                "> 序列号: <font color=\"info\">%s</font>",
                businessSn,
                storageSku,
                invalidFormatSerials.size(),
                String.join(", ", invalidFormatSerials)
            );
            workWeChatBusiness.sendMarkdown(markdown, weChatRobotConfig.getSerialAlarmKey());
        } catch (Exception e) {
            log.error("发送序列号格式不符合预警失败: {}", e.getMessage());
        }
    }

    /**
     * 验证序列号格式
     */
    protected List<String> validateSerialNumbers(List<String> serialList, String businessSn, String storageSku) {
        List<String> validSerials = new ArrayList<>();
        List<String> invalidFormatSerials = new ArrayList<>();

        for (String serialNo : serialList) {
            try {
                if (StringUtils.isEmpty(serialNo)) {
                    log.warn("发现空序列号，已跳过处理");
                    continue;
                }

                if (!SERIAL_PATTERN.matcher(serialNo).matches()) {
                    invalidFormatSerials.add(serialNo);
                }

                validSerials.add(serialNo);
            } catch (Exception e) {
                log.warn("验证序列号时发生异常，仍正常处理: {}，错误: {}", serialNo, e.getMessage());
                validSerials.add(serialNo);
            }
        }

        // 处理格式不符合的序列号预警
        handleInvalidFormatSerialWarning(invalidFormatSerials, businessSn, storageSku);

        if (validSerials.size() != serialList.size()) {
            log.warn("序列号验证完成，原始数量: {}，处理数量: {}（已过滤空序列号）",
                    serialList.size(), validSerials.size());
        }

        return validSerials;
    }
}