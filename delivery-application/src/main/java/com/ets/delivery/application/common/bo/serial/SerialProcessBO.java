package com.ets.delivery.application.common.bo.serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;

import java.util.List;

/**
 * 序列号处理业务对象
 * 用于封装序列号处理方法的参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SerialProcessBO {

    /**
     * 序列号列表
     */
    private List<String> serialList;

    /**
     * 业务单号（发货单号、入库单号等）
     */
    private String businessSn;

    /**
     * 仓库SKU编码
     */
    private String storageSku;

    /**
     * 库存类型（ZP/CC/JS/XS等）
     */
    private String inventoryType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 业务来源
     */
    private String businessSource;

    /**
     * 目标状态
     */
    private SerialStatusEnum targetStatus;

    /**
     * 移除序列号时的状态（用于processRemovedSerials方法）
     */
    private SerialStatusEnum removedStatus;


}