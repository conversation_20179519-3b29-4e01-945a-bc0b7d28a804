package com.ets.delivery.application.app.factory.serial.impl;

import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.infra.entity.StorageRecordSerial;
import com.ets.delivery.application.infra.service.StorageRecordSerialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 寄回序列号处理器
 */
@Slf4j
@Component
public class ReturnSerial extends SerialBase {
    
    @Autowired
    private StorageRecordSerialService storageRecordSerialService;
    

    
    @Override
    protected void setTargetStatus(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.RETURNED);
    }
    
    @Override
    protected void executeSpecificLogic(SerialProcessResult result, SerialProcessBO processBO) {
        // 处理不存在序列号的预警
        handleMissingSerialWarning(result.getNewSerials(), processBO);

        updateStorageRecordSerial(processBO.getSerialList(), processBO.getBusinessSn());
    }
    
    /**
     * 更新StorageRecordSerial表数据（寄回处理）
     */
    private void updateStorageRecordSerial(List<String> serialList, String businessSn) {
        try {
            log.info("开始更新StorageRecordSerial表数据: businessSn={}, 序列号数量={}", businessSn, serialList.size());
            
            // 批量创建StorageRecordSerial记录
            List<StorageRecordSerial> storageRecordSerials = serialList.stream()
                    .map(serialNo -> {
                        StorageRecordSerial storageRecordSerial = new StorageRecordSerial();
                        storageRecordSerial.setSerialNo(serialNo);
                        storageRecordSerial.setRecordSn(businessSn);
                        storageRecordSerial.setCreatedAt(LocalDateTime.now());
                        storageRecordSerial.setUpdatedAt(LocalDateTime.now());
                        return storageRecordSerial;
                    })
                    .collect(Collectors.toList());
            
            if (!storageRecordSerials.isEmpty()) {
                storageRecordSerialService.saveBatch(storageRecordSerials);
                log.info("成功保存StorageRecordSerial记录: 数量={}", storageRecordSerials.size());
            }
            
        } catch (Exception e) {
            log.error("更新StorageRecordSerial表数据失败: businessSn={}", businessSn, e);
        }
    }
}